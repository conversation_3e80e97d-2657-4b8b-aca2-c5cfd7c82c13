package com.zibbava.edgemind;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.awt.*;
import java.net.URI;


/**
 * 端智 AI 助手主启动类
 * 轻量级启动，无需Docker和Redis依赖
 */
@SpringBootApplication
@MapperScan("com.zibbava.**.mapper")
@EnableScheduling
public class EdgemindCortexApplication {



	public static void main(String[] args) {
		System.out.println("EdgeMind-Cortex 启动中...");
		System.out.println("使用轻量级模式：H2数据库 + 内存缓存");

		// 启动Spring Boot应用
		ConfigurableApplicationContext context = SpringApplication.run(EdgemindCortexApplication.class, args);

		// 在应用启动后打开浏览器
		openBrowserOnStartup(context);
	}

	/**
	 * 在应用启动后打开浏览器
	 * @param context 应用上下文
	 */
	private static void openBrowserOnStartup(ConfigurableApplicationContext context) {
		// 检查是否启用自动打开浏览器功能
		String autoOpenBrowser = context.getEnvironment().getProperty("app.auto-open-browser", "true");
		if (!"true".equalsIgnoreCase(autoOpenBrowser)) {
			System.out.println("自动打开浏览器功能已禁用。");
			return;
		}

		// 检查GUI环境是否可用
		boolean isHeadless = GraphicsEnvironment.isHeadless();
		System.out.println("GUI环境检测: isHeadless=" + isHeadless);

		// 获取运行环境
		String runEnv = context.getEnvironment().getProperty("run.env", "prod");
		System.out.println("运行环境: " + runEnv);

		// 在开发环境中，即使检测为headless也尝试打开浏览器
		if (isHeadless && !"dev".equals(runEnv)) {
			System.out.println("检测到无头环境且非开发模式，跳过打开浏览器。");
			return;
		}

		// 检查Desktop是否支持
		boolean desktopSupported = Desktop.isDesktopSupported();
		System.out.println("Desktop支持检测: isDesktopSupported=" + desktopSupported);

		// 在开发环境中，即使Desktop不支持也尝试使用系统命令打开浏览器
		if (!desktopSupported && !"dev".equals(runEnv)) {
			System.out.println("当前系统不支持Desktop功能且非开发模式，无法自动打开浏览器。");
			return;
		}

		final Desktop desktop;
		final boolean canUseBrowser;

//		if (desktopSupported) {
			desktop = Desktop.getDesktop();
			canUseBrowser = desktop.isSupported(Desktop.Action.BROWSE);
			System.out.println("浏览器操作支持检测: canUseBrowser=" + canUseBrowser);
//		} else {
//			desktop = null;
//			canUseBrowser = false;
//		}

		// 从环境中获取端口，但使用固定的目标路径
		String port = context.getEnvironment().getProperty("server.port", "8080");
		String contextPath = context.getEnvironment().getProperty("server.servlet.context-path", "/wkg");
		final String url = String.format("http://localhost:%s%s/", port, contextPath);

		System.out.println("应用启动完成，正在尝试打开浏览器访问: " + url);

		// 使用新线程延迟打开浏览器，确保Web服务器完全启动
		new Thread(() -> {
			try {
				// 等待2秒确保服务器完全启动
				Thread.sleep(2000);

				boolean success = false;

				// 首先尝试使用Desktop API
				if (canUseBrowser && desktop != null) {
					try {
						desktop.browse(new URI(url));
						success = true;
						System.out.println("成功使用Desktop API打开浏览器。");
					} catch (Exception e) {
						System.out.println("Desktop API打开浏览器失败，尝试系统命令: " + e.getMessage());
					}
				}

				// 如果Desktop API失败，尝试使用系统命令
				if (!success) {
					success = openBrowserWithSystemCommand(url);
				}

				if (!success) {
					// 提供手动打开的提示
					System.out.println("=".repeat(60));
					System.out.println("自动打开浏览器失败，请手动在浏览器中访问以下地址:");
					System.out.println(url);
					System.out.println("=".repeat(60));
				}

			} catch (InterruptedException e) {
				System.err.println("等待过程被中断: " + e.getMessage());
				Thread.currentThread().interrupt();
			} catch (Exception e) {
				System.err.println("打开浏览器过程中发生异常: " + e.getMessage());
			}
		}, "BrowserOpener").start();
	}

	/**
	 * 使用系统命令打开浏览器
	 * @param url 要打开的URL
	 * @return 是否成功
	 */
	private static boolean openBrowserWithSystemCommand(String url) {
		try {
			String os = System.getProperty("os.name").toLowerCase();
			System.out.println("检测到操作系统: " + os);

			ProcessBuilder pb;
			if (os.contains("win")) {
				// Windows系统
				pb = new ProcessBuilder("cmd", "/c", "start", "\"\"", url);
			} else if (os.contains("mac")) {
				// macOS系统
				pb = new ProcessBuilder("open", url);
			} else {
				// Linux系统
				pb = new ProcessBuilder("xdg-open", url);
			}

			Process process = pb.start();
			// 等待一小段时间检查进程是否成功启动
			Thread.sleep(500);

			if (process.isAlive() || process.exitValue() == 0) {
				System.out.println("成功使用系统命令打开浏览器。");
				return true;
			} else {
				System.out.println("系统命令执行失败，退出码: " + process.exitValue());
				return false;
			}
		} catch (Exception e) {
			System.out.println("使用系统命令打开浏览器失败: " + e.getMessage());
			return false;
		}
	}
}
