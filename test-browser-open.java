import java.awt.*;
import java.net.URI;

/**
 * 测试浏览器自动打开功能
 */
public class TestBrowserOpen {
    public static void main(String[] args) {
        System.out.println("测试浏览器自动打开功能...");
        
        // 检查GUI环境
        if (GraphicsEnvironment.isHeadless()) {
            System.out.println("检测到无头环境，跳过打开浏览器。");
            return;
        }
        
        // 检查Desktop支持
        if (!Desktop.isDesktopSupported()) {
            System.out.println("当前系统不支持Desktop功能。");
            return;
        }
        
        Desktop desktop = Desktop.getDesktop();
        if (!desktop.isSupported(Desktop.Action.BROWSE)) {
            System.out.println("当前系统不支持浏览器操作。");
            return;
        }
        
        try {
            String url = "http://localhost:8080/wkg/";
            System.out.println("尝试打开浏览器访问: " + url);
            desktop.browse(new URI(url));
            System.out.println("成功打开浏览器！");
        } catch (Exception e) {
            System.err.println("打开浏览器失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
